{"LABEL": {"name": "title needs [type]", "color": "EEEEEE"}, "CHECKS": {"prefixes": ["[BugFix]", "[Enhancement]", "[Feature]", "[Refactor]", "[UT]", "[Doc]", "[Tool]", "<PERSON><PERSON>"], "regexpFlags": "i", "ignoreLabels": ["ignore-pr-title-check", "pass"]}, "MESSAGES": {"success": "All OK", "failure": "Missing type in the title of the pull request. [BugFix], [Enhancement], [Feature], [Refactor], [UT], [Doc], [Tool]. \nExample: [BugFix] Fix some implicit conversion.", "notice": ""}}