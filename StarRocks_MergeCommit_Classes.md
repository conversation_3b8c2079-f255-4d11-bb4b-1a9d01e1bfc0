# StarRocks Merge Commit 相关类汇总

## 1. 核心管理类

### MergeCommitJob
- **路径**: `fe/fe-core/src/main/java/com/starrocks/load/batchwrite/MergeCommitJob.java`
- **功能**: 负责管理具有同构参数的加载请求，创建加载任务来合并这些请求，并监控这些任务的状态
- **关键方法**:
  - `requestLoad()`: 请求来自指定后端的写操作加载
  - `finish()`: 任务完成时的回调处理

### MergeCommitTask
- **路径**: `fe/fe-core/src/main/java/com/starrocks/load/batchwrite/MergeCommitTask.java`
- **功能**: 具体的合并提交任务执行器
- **关键方法**:
  - `run()`: 主要执行流程 - beginTxn() -> executeLoad() -> commitAndPublishTxn()
  - `beginTxn()`: 开始事务
  - `commitAndPublishTxn()`: 提交并发布事务

### MergeCommitTaskCallback
- **路径**: `fe/fe-core/src/main/java/com/starrocks/load/batchwrite/MergeCommitTaskCallback.java`
- **功能**: 加载执行的回调接口
- **关键方法**:
  - `finish()`: 任务完成时调用

## 2. 事务管理类

### GlobalTransactionMgr
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/GlobalTransactionMgr.java`
- **功能**: 全局事务管理器，负责事务的开始、提交、中止
- **关键方法**:
  - `beginTransaction()`: 开始事务
  - `commitTransaction()`: 提交事务
  - `finishTransaction()`: 完成事务

### DatabaseTransactionMgr
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/DatabaseTransactionMgr.java`
- **功能**: 数据库级别的事务管理器
- **关键方法**:
  - `beginTransaction()`: 开始数据库级别的事务
  - `commitTransaction()`: 提交数据库级别的事务
  - `finishTransactionNew()`: 使用新机制完成事务

### TransactionState
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TransactionState.java`
- **功能**: 事务状态管理，包含事务的完整生命周期信息
- **关键属性**:
  - `transactionStatus`: 事务状态 (PREPARE, COMMITTED, VISIBLE, ABORTED, PREPARED)
  - `tableIdList`: 涉及的表ID列表
  - `idToTableCommitInfos`: 表提交信息映射
- **关键方法**:
  - `setTransactionStatus()`: 设置事务状态
  - `beforeStateTransform()`: 状态转换前的回调
  - `afterStateTransform()`: 状态转换后的回调

### TransactionStatus
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TransactionStatus.java`
- **功能**: 事务状态枚举
- **状态值**:
  - UNKNOWN(0): 未知状态
  - PREPARE(1): 准备状态
  - COMMITTED(2): 已提交状态
  - VISIBLE(3): 可见状态
  - ABORTED(4): 已中止状态
  - PREPARED(5): 已准备状态

## 3. 提交信息类

### TabletCommitInfo
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TabletCommitInfo.java`
- **功能**: 平板提交信息，包含平板ID、后端ID等
- **关键属性**:
  - `tabletId`: 平板ID
  - `backendId`: 后端ID
  - `invalidDictCacheColumns`: 无效字典缓存列
  - `validDictCacheColumns`: 有效字典缓存列

### TableCommitInfo
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TableCommitInfo.java`
- **功能**: 表提交信息，包含分区提交信息映射
- **关键属性**:
  - `tableId`: 表ID
  - `idToPartitionCommitInfo`: 分区提交信息映射

### TxnCommitAttachment
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TxnCommitAttachment.java`
- **功能**: 事务提交附件，根据不同的加载作业源类型创建不同的附件
- **支持的类型**:
  - ROUTINE_LOAD_TASK: 例行加载任务
  - BATCH_LOAD_JOB: 批量加载作业
  - BACKEND_STREAMING: 后端流式加载
  - FRONTEND_STREAMING: 前端流式加载

## 4. 版本发布类

### PublishVersionDaemon
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/PublishVersionDaemon.java`
- **功能**: 版本发布守护进程，负责将已提交的事务版本发布到所有后端
- **关键方法**:
  - `runAfterCatalogReady()`: 目录就绪后运行
  - `publishVersionForOlapTable()`: 为OLAP表发布版本
  - `publishVersionForLakeTable()`: 为Lake表发布版本

## 5. 后端事务管理类

### TxnManager (BE)
- **路径**: `be/src/storage/txn_manager.h` 和 `be/src/storage/txn_manager.cpp`
- **功能**: 后端事务管理器，管理平板和事务之间的映射
- **关键方法**:
  - `prepare_txn()`: 准备事务
  - `commit_txn()`: 提交事务
  - `publish_txn()`: 发布事务

### PublishVersionManager (BE)
- **路径**: `be/src/storage/publish_version_manager.h` 和 `be/src/storage/publish_version_manager.cpp`
- **功能**: 后端版本发布管理器
- **关键方法**:
  - `wait_publish_task_apply_finish()`: 等待发布任务应用完成
  - `update_tablet_version()`: 更新平板版本

## 6. 监听器和回调类

### OlapTableTxnStateListener
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/OlapTableTxnStateListener.java`
- **功能**: OLAP表事务状态监听器
- **关键方法**:
  - `preCommit()`: 提交前处理
  - `preWriteCommitLog()`: 写提交日志前处理
  - `postAbort()`: 中止后处理

### TransactionStateListener
- **功能**: 事务状态监听器接口，定义事务状态变化时的回调方法

## 7. 辅助类

### TransactionGraph
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TransactionGraph.java`
- **功能**: 事务依赖图，用于管理事务之间的依赖关系

### TransactionStateBatch
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TransactionStateBatch.java`
- **功能**: 事务状态批处理，用于批量处理多个事务状态

### TransactionStateSnapshot
- **路径**: `fe/fe-core/src/main/java/com/starrocks/transaction/TransactionStateSnapshot.java`
- **功能**: 事务状态快照，包含事务状态和原因信息
