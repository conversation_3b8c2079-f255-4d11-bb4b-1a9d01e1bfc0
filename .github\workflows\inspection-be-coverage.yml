name: <PERSON><PERSON> COVERAGE IN INSPECTION PIPELINE

on:
  workflow_call:
    inputs:
      BRANCH:
        description: 'BRANCH'
        required: true
        type: string
      COMMIT_ID:
        description: 'COMMIT ID'
        required: true
        type: string
      DIFF_FILE_PATH:
        description: 'FILE PATH OF DIFF INFO'
        required: true
        type: string

    outputs:
      BE_COV_RES_FILE:
        description: "BE COV RESULT FILE"
        value: ${{ jobs.REPORT.outputs.BE_COV_RES_FILE }}

permissions:
  checks: write
  actions: write
  contents: write
  deployments: write
  discussions: write
  issues: write
  packages: write
  pages: write
  pull-requests: write
  repository-projects: write
  security-events: write
  statuses: write

env:
  IS_INSPECTION: true
  BASE_REF: ${{ inputs.BRANCH }}
  COMMIT_ID: ${{ inputs.COMMIT_ID }}
  DIFF_FILE_PATH: ${{ inputs.DIFF_FILE_PATH }}
  bucket_prefix: starrocks


jobs:
  REPORT:
    runs-on: [ self-hosted, quick ]
    env:
      OSS_CMD: "ossutil64 --config-file /root/.ossutilconfig"
      JAVA_HOME: /var/local/env/jdk1.8.0_202
    outputs:
      BE_COV_RES_FILE: ${{ steps.incremental_cov_report.outputs.BE_COV_RES_FILE }}

    steps:
      - name: CLEAN & ENV
        run: |
          rm -rf ${{ github.workspace }} && mkdir -p ${{ github.workspace }}
          if [[ ! -e "${DIFF_FILE_PATH}" ]]; then
            echo "::error::diff file: ${DIFF_FILE_PATH} not exist!"
            exit 1
          fi

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ env.BASE_REF }}

      - name: checkout commit
        run: |
          git reset ${{ env.COMMIT_ID }} --hard

      - name: Download BE UT XML
        id: download-ut-xml
        run: |
          oss_path=oss://${bucket_prefix}-ci-release/$BASE_REF/Release/pr/UT-Report/${COMMIT_ID}
          be_ut_res_path=${oss_path}/flag/be_ut_res
          size=$(${OSS_CMD} ls ${be_ut_res_path} | grep "Object Number is" | awk '{print $NF}')
          if [[ "$size" == "0" ]]; then
            echo "::error::BE UT result not exit!"
            exit 1
          fi
          
          ${OSS_CMD} cp ${be_ut_res_path} . 
          res=`cat be_ut_res`
          if [[ "$res" != "0" ]]; then
            echo "::error::BE UT failed!"
            exit 1
          fi
          
          cd be
          ${OSS_CMD} cp ${oss_path}/be_ut_coverage.xml . -f 1>/dev/null

      - name: Merge BE Coverage
        id: merge_report
        env:
          IGNORE_CODECOV: true
        run: |
          rm -rf ./ci-tool && cp -rf /var/lib/ci-tool ./ci-tool && cd ci-tool && git pull && source lib/init.sh

          export ECI_ID=`./bin/create_eci.sh ${BASE_REF} ${GITHUB_REPOSITORY}`
          export TOTAL_COV_XML=/var/local/env/be_total_coverage_${COMMIT_ID}.xml
          export COV_LOG=/var/local/env/be_total_coverage_${COMMIT_ID}.log
          export RES_FILE=/var/local/env/be_total_coverage_res_${COMMIT_ID}.log

          echo "ECI_ID=${ECI_ID}" | tee -a $GITHUB_OUTPUT
          echo "COV_XML=${TOTAL_COV_XML}" | tee -a $GITHUB_OUTPUT
          echo "COV_LOG=${COV_LOG}" | tee -a $GITHUB_OUTPUT
          echo "RES_FILE=${RES_FILE}" | tee -a $GITHUB_OUTPUT

          ./bin/gen_be_cov.sh ${GITHUB_REPOSITORY} ${BASE_REF} ${COMMIT_ID} ${COMMIT_ID}

      # Incremental Total Coverage
      - name: Incremental Coverage Report - Total
        id: incremental_cov_report
        env:
          be_path: ${{ github.workspace }}/be
          total_xml: ${{ steps.merge_report.outputs.COV_XML }}
        run: |
          rm -rf ./coverchecker && ln -s /var/local/env/coverchecker ./coverchecker && cd coverchecker && git pull
          export PATH=$JAVA_HOME/bin:$PATH;
          java -jar cover-checker-console/target/cover-checker-console-1.4.0-jar-with-dependencies.jar \
            --cover ${total_xml} \
            --threshold 80 \
            -type cobertura \
            --module BE \
            -d ${DIFF_FILE_PATH} -dt file  | tee "${DIFF_FILE_PATH}_be_cov.log"
          
          echo "BE_COV_RES_FILE=${DIFF_FILE_PATH}_be_cov.log" >> $GITHUB_OUTPUT

      - name: Clean ENV
        if: always()
        run: |
          rm -rf ${{ github.workspace }}/*
          eci rm ${{ steps.merge_report.outputs.ECI_ID }} || true
          rm -rf ${{ steps.merge_report.outputs.COV_XML }} ${{ steps.merge_report.outputs.COV_LOG }} ${{ steps.merge_report.outputs.RES_FILE }}